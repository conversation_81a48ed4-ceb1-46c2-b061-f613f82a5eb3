<?php

namespace App\Core;

/**
 * Response Class
 * সব ফিচারের জন্য কমন রেসপন্স হ্যান্ডলার
 */
class Response
{
    private $statusCode = 200;
    private $headers = [];
    private $content = '';

    /**
     * Set HTTP status code
     */
    public function setStatusCode($code)
    {
        $this->statusCode = $code;
        return $this;
    }

    /**
     * Set response header
     */
    public function setHeader($name, $value)
    {
        $this->headers[$name] = $value;
        return $this;
    }

    /**
     * Set multiple headers
     */
    public function setHeaders(array $headers)
    {
        foreach ($headers as $name => $value) {
            $this->setHeader($name, $value);
        }
        return $this;
    }

    /**
     * Send JSON response
     */
    public function json($data, $statusCode = null)
    {
        if ($statusCode !== null) {
            $this->setStatusCode($statusCode);
        }

        $this->setHeader('Content-Type', 'application/json');
        $this->content = json_encode($data, JSON_UNESCAPED_UNICODE);
        
        return $this->send();
    }

    /**
     * Send HTML response
     */
    public function html($content, $statusCode = null)
    {
        if ($statusCode !== null) {
            $this->setStatusCode($statusCode);
        }

        $this->setHeader('Content-Type', 'text/html; charset=utf-8');
        $this->content = $content;
        
        return $this->send();
    }

    /**
     * Render view file
     */
    public function view($viewPath, $data = [], $statusCode = null)
    {
        if ($statusCode !== null) {
            $this->setStatusCode($statusCode);
        }

        $viewFile = BASE_PATH . '/resources/views/' . str_replace('.', '/', $viewPath) . '.php';
        
        if (!file_exists($viewFile)) {
            throw new \Exception("View file not found: {$viewFile}");
        }

        // Extract data to variables
        extract($data);
        
        // Start output buffering
        ob_start();
        include $viewFile;
        $content = ob_get_clean();
        
        return $this->html($content);
    }

    /**
     * Redirect to URL
     */
    public function redirect($url, $statusCode = 302)
    {
        $this->setStatusCode($statusCode);
        $this->setHeader('Location', $url);
        
        return $this->send();
    }

    /**
     * Redirect back
     */
    public function back()
    {
        $referer = $_SERVER['HTTP_REFERER'] ?? '/';
        return $this->redirect($referer);
    }

    /**
     * Send file download
     */
    public function download($filePath, $fileName = null)
    {
        if (!file_exists($filePath)) {
            throw new \Exception("File not found: {$filePath}");
        }

        $fileName = $fileName ?? basename($filePath);
        $fileSize = filesize($filePath);
        $mimeType = mime_content_type($filePath) ?: 'application/octet-stream';

        $this->setHeaders([
            'Content-Type' => $mimeType,
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Content-Length' => $fileSize,
            'Cache-Control' => 'must-revalidate',
            'Pragma' => 'public',
            'Expires' => '0'
        ]);

        $this->content = file_get_contents($filePath);
        
        return $this->send();
    }

    /**
     * Send error response
     */
    public function error($message, $statusCode = 500, $data = [])
    {
        $response = array_merge([
            'error' => true,
            'message' => $message,
            'status_code' => $statusCode
        ], $data);

        return $this->json($response, $statusCode);
    }

    /**
     * Send success response
     */
    public function success($message = 'Success', $data = [], $statusCode = 200)
    {
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data
        ];

        return $this->json($response, $statusCode);
    }

    /**
     * Send validation error response
     */
    public function validationError($errors, $message = 'Validation failed')
    {
        return $this->json([
            'error' => true,
            'message' => $message,
            'errors' => $errors
        ], 422);
    }

    /**
     * Send the response
     */
    public function send()
    {
        // Set status code
        http_response_code($this->statusCode);
        
        // Set headers
        foreach ($this->headers as $name => $value) {
            header("{$name}: {$value}");
        }
        
        // Send content
        echo $this->content;
        
        // End execution
        exit;
    }

    /**
     * Set CORS headers
     */
    public function cors($origins = '*', $methods = 'GET,POST,PUT,DELETE,OPTIONS', $headers = '*')
    {
        $this->setHeaders([
            'Access-Control-Allow-Origin' => $origins,
            'Access-Control-Allow-Methods' => $methods,
            'Access-Control-Allow-Headers' => $headers,
            'Access-Control-Max-Age' => '86400'
        ]);

        return $this;
    }

    /**
     * Handle preflight OPTIONS request
     */
    public function handlePreflight()
    {
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            $this->cors()->send();
        }
        
        return $this;
    }
}
