<?php
/**
 * Stats Section - Dynamic and SEO Optimized
 */

// Dynamic stats data (in real app, fetch from database)
$statsData = [
    [
        'number' => '1,000+',
        'label' => 'Happy Users',
        'icon' => 'fas fa-users',
        'color' => 'primary',
        'description' => 'Active users earning daily'
    ],
    [
        'number' => '৳50,000+',
        'label' => 'Total Paid',
        'icon' => 'fas fa-money-bill-wave',
        'color' => 'success',
        'description' => 'Total amount paid to users'
    ],
    [
        'number' => '5,000+',
        'label' => 'Quizzes Completed',
        'icon' => 'fas fa-brain',
        'color' => 'info',
        'description' => 'Successful quiz completions'
    ],
    [
        'number' => '10,000+',
        'label' => 'Social Interactions',
        'icon' => 'fas fa-heart',
        'color' => 'warning',
        'description' => 'Posts, likes, and comments'
    ]
];
?>

<!-- Stats Section -->
<section class="stats-section bg-white py-5" id="stats">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="display-6 fw-bold text-dark">Our Success in Numbers</h2>
            <p class="lead text-muted">See how our community is growing and earning together</p>
        </div>

        <div class="row g-4">
            <?php foreach ($statsData as $index => $stat): ?>
            <div class="col-md-6 col-lg-3">
                <div class="stat-item text-center p-4 h-100 border-0 shadow-sm rounded-3 bg-light">
                    <div class="stat-icon mb-3">
                        <i class="<?= $stat['icon'] ?> fa-3x text-<?= $stat['color'] ?>"></i>
                    </div>
                    <div class="stat-number display-4 fw-bold text-<?= $stat['color'] ?> mb-2"
                         data-target="<?= filter_var($stat['number'], FILTER_SANITIZE_NUMBER_INT) ?>"
                         data-suffix="<?= preg_replace('/[0-9,]/', '', $stat['number']) ?>">
                        0
                    </div>
                    <h5 class="stat-label fw-semibold text-dark mb-2"><?= htmlspecialchars($stat['label']) ?></h5>
                    <p class="text-muted small mb-0"><?= htmlspecialchars($stat['description']) ?></p>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
