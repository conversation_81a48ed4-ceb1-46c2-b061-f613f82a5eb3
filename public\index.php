<?php
/**
 * JobSpace - Online Earning Platform
 * Public Entry Point
 */

// Define constants
define('BASE_PATH', dirname(__DIR__));
define('APP_PATH', BASE_PATH . '/app');
define('CONFIG_PATH', BASE_PATH . '/config');
define('VIEWS_PATH', BASE_PATH . '/resources/views');
define('PUBLIC_PATH', __DIR__);

// Load configuration
$config = require_once CONFIG_PATH . '/app.php';

// Error reporting (always on for debugging)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Autoloader
require_once BASE_PATH . '/bootstrap/autoload.php';

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Initialize application
try {
    $request = new App\Core\Request();
    $response = new App\Core\Response();
    $router = new App\Core\Router($request, $response);

    // Load routes
    require_once BASE_PATH . '/routes/web.php';

    // Dispatch request
    $router->dispatch();

} catch (Exception $e) {
    // Professional error handling
    error_log("JobSpace Error: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());

    http_response_code(500);

    if ($config['debug']) {
        echo "<h1>Application Error</h1>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    } else {
        echo "<h1>Something went wrong</h1>";
        echo "<p>We're working to fix this issue. Please try again later.</p>";
    }
}
