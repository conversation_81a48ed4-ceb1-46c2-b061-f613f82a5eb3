<?php

namespace App\Core;

/**
 * Main Application Class
 * সব ফিচারের জন্য কমন অ্যাপ ক্লাস
 */
class App
{
    private static $instance = null;
    private $config = [];
    private $services = [];

    public function __construct()
    {
        self::$instance = $this;
        $this->loadConfig();
        $this->registerServices();
    }

    /**
     * Singleton instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Load all configuration files
     */
    private function loadConfig()
    {
        $configPath = BASE_PATH . '/config';
        $configFiles = ['app', 'database', 'auth', 'mail', 'payment'];

        foreach ($configFiles as $file) {
            $filePath = $configPath . '/' . $file . '.php';
            if (file_exists($filePath)) {
                $this->config[$file] = require $filePath;
            }
        }
    }

    /**
     * Register common services for all features
     */
    private function registerServices()
    {
        // Database service
        $this->services['database'] = function() {
            return Database::getInstance();
        };

        // Auth service
        $this->services['auth'] = function() {
            return new Auth();
        };

        // Wallet service (সব ফিচারের জন্য)
        $this->services['wallet'] = function() {
            return new \App\Services\WalletService();
        };

        // Email service
        $this->services['email'] = function() {
            return new \App\Services\EmailService();
        };

        // Notification service
        $this->services['notification'] = function() {
            return new \App\Services\NotificationService();
        };
    }

    /**
     * Get configuration value
     */
    public function config($key, $default = null)
    {
        $keys = explode('.', $key);
        $value = $this->config;

        foreach ($keys as $k) {
            if (isset($value[$k])) {
                $value = $value[$k];
            } else {
                return $default;
            }
        }

        return $value;
    }

    /**
     * Get service instance
     */
    public function service($name)
    {
        if (isset($this->services[$name])) {
            if (is_callable($this->services[$name])) {
                $this->services[$name] = $this->services[$name]();
            }
            return $this->services[$name];
        }

        throw new \Exception("Service '{$name}' not found");
    }

    /**
     * Register a new service (for features)
     */
    public function registerService($name, $callback)
    {
        $this->services[$name] = $callback;
    }
}
