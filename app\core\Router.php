<?php

namespace App\Core;

/**
 * Router Class
 * সব ফিচারের জন্য কমন রাউটার
 */
class Router
{
    private $routes = [];
    private $request;
    private $response;

    public function __construct(Request $request, Response $response)
    {
        $this->request = $request;
        $this->response = $response;
    }

    /**
     * Add GET route
     */
    public function get($path, $handler)
    {
        $this->addRoute('GET', $path, $handler);
    }

    /**
     * Add POST route
     */
    public function post($path, $handler)
    {
        $this->addRoute('POST', $path, $handler);
    }

    /**
     * Add PUT route
     */
    public function put($path, $handler)
    {
        $this->addRoute('PUT', $path, $handler);
    }

    /**
     * Add DELETE route
     */
    public function delete($path, $handler)
    {
        $this->addRoute('DELETE', $path, $handler);
    }

    /**
     * Add route to routes array
     */
    private function addRoute($method, $path, $handler)
    {
        $this->routes[] = [
            'method' => $method,
            'path' => $path,
            'handler' => $handler
        ];
    }

    /**
     * Feature group routes (for organizing feature routes)
     */
    public function group($prefix, $callback)
    {
        $originalRoutes = $this->routes;
        $this->routes = [];
        
        // Execute callback to collect routes
        $callback($this);
        
        // Add prefix to collected routes
        foreach ($this->routes as &$route) {
            $route['path'] = $prefix . $route['path'];
        }
        
        // Merge with original routes
        $this->routes = array_merge($originalRoutes, $this->routes);
    }

    /**
     * Dispatch request to appropriate handler
     */
    public function dispatch()
    {
        $requestMethod = $this->request->getMethod();
        $requestPath = $this->request->getPath();

        foreach ($this->routes as $route) {
            if ($route['method'] === $requestMethod && $this->matchPath($route['path'], $requestPath)) {
                return $this->executeHandler($route['handler'], $requestPath, $route['path']);
            }
        }

        // Route not found
        $this->response->setStatusCode(404);
        $this->response->json(['error' => 'Route not found']);
    }

    /**
     * Match route path with request path
     */
    private function matchPath($routePath, $requestPath)
    {
        // Convert route path to regex pattern
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        return preg_match($pattern, $requestPath);
    }

    /**
     * Execute route handler
     */
    private function executeHandler($handler, $requestPath, $routePath)
    {
        // Extract parameters from URL
        $params = $this->extractParams($routePath, $requestPath);

        if (is_string($handler)) {
            // Handle "Controller@method" format
            if (strpos($handler, '@') !== false) {
                [$controllerClass, $method] = explode('@', $handler);
                
                // Check if it's a feature controller
                if (strpos($controllerClass, 'Features\\') === 0) {
                    $controllerClass = 'App\\' . $controllerClass;
                } else {
                    $controllerClass = 'App\\Controllers\\' . $controllerClass;
                }

                if (class_exists($controllerClass)) {
                    $controller = new $controllerClass();
                    if (method_exists($controller, $method)) {
                        return call_user_func_array([$controller, $method], $params);
                    }
                }
            }
        } elseif (is_callable($handler)) {
            // Handle closure
            return call_user_func_array($handler, $params);
        }

        throw new \Exception("Handler not found for route: {$routePath}");
    }

    /**
     * Extract parameters from URL
     */
    private function extractParams($routePath, $requestPath)
    {
        $routeParts = explode('/', trim($routePath, '/'));
        $requestParts = explode('/', trim($requestPath, '/'));
        $params = [];

        for ($i = 0; $i < count($routeParts); $i++) {
            if (isset($routeParts[$i]) && preg_match('/\{([^}]+)\}/', $routeParts[$i], $matches)) {
                $paramName = $matches[1];
                $params[$paramName] = $requestParts[$i] ?? null;
            }
        }

        return array_values($params);
    }

    /**
     * Load feature routes dynamically (disabled for now)
     */
    public function loadFeatureRoutes()
    {
        // Disabled for step-by-step approach
        // Will be implemented later when features are ready
        return;
    }
}
