<?php

namespace App\Core;

/**
 * Authentication Class
 * সব ফিচারের জন্য কমন অথেন্টিকেশন সিস্টেম
 */
class Auth
{
    private $db;
    private $sessionKey = 'user_id';

    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->startSession();
    }

    /**
     * Start session if not already started
     */
    private function startSession()
    {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * Login user
     */
    public function login($email, $password, $remember = false)
    {
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE email = ? AND status = 'active'",
            [$email]
        );

        if ($user && password_verify($password, $user['password'])) {
            // Set session
            $_SESSION[$this->sessionKey] = $user['id'];
            $_SESSION['user_data'] = [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role'] ?? 'user'
            ];

            // Update last login
            $this->db->update('users', [
                'last_login' => date('Y-m-d H:i:s'),
                'login_count' => ($user['login_count'] ?? 0) + 1
            ], 'id = ?', [$user['id']]);

            // Set remember me cookie if requested
            if ($remember) {
                $this->setRememberToken($user['id']);
            }

            return true;
        }

        return false;
    }

    /**
     * Register new user
     */
    public function register($data)
    {
        // Check if email already exists
        $existingUser = $this->db->fetch(
            "SELECT id FROM users WHERE email = ?",
            [$data['email']]
        );

        if ($existingUser) {
            throw new \Exception('Email already exists');
        }

        // Hash password
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['status'] = 'active';
        $data['role'] = $data['role'] ?? 'user';

        // Insert user
        $userId = $this->db->insert('users', $data);

        // Create wallet for user
        $this->createUserWallet($userId);

        return $userId;
    }

    /**
     * Create wallet for new user
     */
    private function createUserWallet($userId)
    {
        $this->db->insert('wallets', [
            'user_id' => $userId,
            'balance' => 0.00,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Logout user
     */
    public function logout()
    {
        // Clear session
        unset($_SESSION[$this->sessionKey]);
        unset($_SESSION['user_data']);
        
        // Clear remember me cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
        }

        // Destroy session if empty
        if (empty($_SESSION)) {
            session_destroy();
        }
    }

    /**
     * Check if user is logged in
     */
    public function check()
    {
        if (isset($_SESSION[$this->sessionKey])) {
            return true;
        }

        // Check remember me token
        if (isset($_COOKIE['remember_token'])) {
            return $this->loginFromRememberToken($_COOKIE['remember_token']);
        }

        return false;
    }

    /**
     * Get current user
     */
    public function user()
    {
        if (!$this->check()) {
            return null;
        }

        return $_SESSION['user_data'] ?? null;
    }

    /**
     * Get current user ID
     */
    public function id()
    {
        $user = $this->user();
        return $user ? $user['id'] : null;
    }

    /**
     * Check if user has role
     */
    public function hasRole($role)
    {
        $user = $this->user();
        return $user && $user['role'] === $role;
    }

    /**
     * Check if user is admin
     */
    public function isAdmin()
    {
        return $this->hasRole('admin');
    }

    /**
     * Set remember me token
     */
    private function setRememberToken($userId)
    {
        $token = bin2hex(random_bytes(32));
        $hashedToken = hash('sha256', $token);
        
        // Store hashed token in database
        $this->db->update('users', [
            'remember_token' => $hashedToken,
            'remember_token_expires' => date('Y-m-d H:i:s', strtotime('+30 days'))
        ], 'id = ?', [$userId]);

        // Set cookie with plain token
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/');
    }

    /**
     * Login from remember token
     */
    private function loginFromRememberToken($token)
    {
        $hashedToken = hash('sha256', $token);
        
        $user = $this->db->fetch(
            "SELECT * FROM users WHERE remember_token = ? AND remember_token_expires > NOW() AND status = 'active'",
            [$hashedToken]
        );

        if ($user) {
            $_SESSION[$this->sessionKey] = $user['id'];
            $_SESSION['user_data'] = [
                'id' => $user['id'],
                'name' => $user['name'],
                'email' => $user['email'],
                'role' => $user['role'] ?? 'user'
            ];

            return true;
        }

        // Invalid token, clear cookie
        setcookie('remember_token', '', time() - 3600, '/');
        return false;
    }

    /**
     * Generate password reset token
     */
    public function generateResetToken($email)
    {
        $user = $this->db->fetch("SELECT id FROM users WHERE email = ?", [$email]);
        
        if (!$user) {
            return false;
        }

        $token = bin2hex(random_bytes(32));
        $hashedToken = hash('sha256', $token);
        
        $this->db->update('users', [
            'reset_token' => $hashedToken,
            'reset_token_expires' => date('Y-m-d H:i:s', strtotime('+1 hour'))
        ], 'id = ?', [$user['id']]);

        return $token;
    }

    /**
     * Reset password with token
     */
    public function resetPassword($token, $newPassword)
    {
        $hashedToken = hash('sha256', $token);
        
        $user = $this->db->fetch(
            "SELECT id FROM users WHERE reset_token = ? AND reset_token_expires > NOW()",
            [$hashedToken]
        );

        if (!$user) {
            return false;
        }

        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        $this->db->update('users', [
            'password' => $hashedPassword,
            'reset_token' => null,
            'reset_token_expires' => null
        ], 'id = ?', [$user['id']]);

        return true;
    }
}
