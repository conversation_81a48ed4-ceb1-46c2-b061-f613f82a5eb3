<?php
// bootstrap/app.php

// 1. Composer Autoload (if exists)
$composerAutoload = __DIR__ . '/../vendor/autoload.php';
if (file_exists($composerAutoload)) {
    require_once $composerAutoload;
}

// 2. Base Path Definition
define('BASE_PATH', realpath(__DIR__ . '/../'));

// 3. Smart Path Registration Function
function registerPath($name, $relativePath)
{
    $fullPath = BASE_PATH . $relativePath;
    if (is_dir($fullPath)) {
        define($name, $fullPath);
    } else {
        define($name, null);
        error_log("⚠️ Warning: [$name] not found → Expected: [$fullPath]");
    }
}

// 4. Register All Possible Core Paths
registerPath('APP_PATH', '/app');
registerPath('CONFIG_PATH', '/config');
registerPath('ROUTES_PATH', '/routes');
registerPath('VIEWS_PATH', '/views');
registerPath('STORAGE_PATH', '/storage');
registerPath('CACHE_PATH', '/storage/cache');
registerPath('LOGS_PATH', '/storage/logs');
registerPath('INCLUDES_PATH', '/includes');
registerPath('TEMPLATES_PATH', '/templates');
registerPath('MODULES_PATH', '/app/Modules');
registerPath('CONTROLLERS_PATH', '/app/Controllers');
registerPath('MODELS_PATH', '/app/Models');
registerPath('HELPERS_PATH', '/app/Helpers');
registerPath('PUBLIC_PATH', '/public');
registerPath('ASSETS_PATH', '/public/assets');

// 5. Load Environment Variables from .env
$envFile = BASE_PATH . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        $line = trim($line);
        if ($line === '' || str_starts_with($line, '#')) continue;
        [$key, $value] = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        $_ENV[$key] = $value;
        putenv("$key=$value");
    }
}

// 6. Optional: Set Default Timezone from .env
if (!empty($_ENV['APP_TIMEZONE'])) {
    date_default_timezone_set($_ENV['APP_TIMEZONE']);
} else {
    date_default_timezone_set('UTC'); // fallback
}

// 7. Global Error Reporting & Handler
error_reporting(E_ALL);
ini_set('display_errors', '1');

set_exception_handler(function ($e) {
    http_response_code(500);
    echo "<h2>⚠️ Fatal Application Error</h2>";
    echo "<strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    exit;
});

// 8. PSR-4 Autoloading (for App\ namespace)
spl_autoload_register(function ($class) {
    $prefix = 'App\\';
    $baseDir = APP_PATH . '/';

    if (strncmp($prefix, $class, strlen($prefix)) !== 0) {
        return;
    }

    $relativeClass = substr($class, strlen($prefix));
    $file = $baseDir . str_replace('\\', '/', $relativeClass) . '.php';

    if (file_exists($file)) {
        require $file;
    } else {
        error_log("❌ Autoload Failed: [$class] → [$file] not found.");
    }
});

// 9. Initialize Core App Class
if (!class_exists('App\Core\App')) {
    die("<h2>❌ Critical: Core App Class Not Found</h2><p>Make sure App\Core\App.php exists.</p>");
}
$app = new App\Core\App();

// 10. Load Routes if available
$routeFile = ROUTES_PATH . '/web.php';
if (file_exists($routeFile)) {
    require_once $routeFile;
} else {
    error_log("⚠️ Warning: Route file not found at [$routeFile]");
}

// 11. Return App (optional)
return $app;
